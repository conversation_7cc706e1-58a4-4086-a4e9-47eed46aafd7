测试原始需求编号,测试原始需求描述,优先级,所属功能模块,需求来源,需求描述（给自己出题）,软件需求ID,软件需求描述,测试需求ID,测试需求描述,测试子项,测试用例编号,测试用例标题,测试步骤,预期结果,测试方法,测试类型,执行状态,实际结果,缺陷ID
新浪财经-001,系统应实时显示股票、基金、期货等金融产品行情数据,高,行情数据,软件需求说明书,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据数据延迟不超过15分钟,R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,股票行情显示,TC001,股票行情正常显示,"1.打开新浪财经首页;2.点击股票菜单;3.查看上证指数行情","显示当前价格、涨跌幅、成交量等信息",等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,基金行情显示,TC002,基金行情正常显示,"1.访问基金页面
2.选择任意基金
3.查看基金净值","显示最新净值、涨跌幅、基金规模等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,期货行情显示,TC003,期货行情正常显示,"1.访问期货页面
2.选择主力合约
3.查看期货价格","显示最新价格、涨跌幅、持仓量等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,外汇行情显示,TC004,外汇行情正常显示,"1.访问外汇页面
2.选择USD/CNY
3.查看汇率信息","显示实时汇率、涨跌幅、买卖价等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,黄金行情显示,TC005,黄金行情正常显示,"1.访问黄金页面
2.查看现货黄金价格
3.验证数据完整性","显示黄金价格、涨跌幅、成交量等信息",高,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,数据自动刷新,TC006,行情数据自动刷新,"1.打开任意行情页面
2.等待15秒
3.观察数据是否更新",数据在15秒内自动刷新,高,边界值分析,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,历史数据查询,TC007,历史数据查询,"1.选择任意股票
2.点击""历史数据""
3.选择查询时间范围",显示指定时间范围的历史行情数据,中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,K线图显示,TC008,K线图正常显示,"1.进入股票详情页
2.查看K线图
3.切换不同时间周期","K线图正常显示，支持日K、周K、月K切换",中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,分时图显示,TC009,分时图正常显示,"1.进入股票详情页
2.点击分时图
3.查看实时走势","分时图实时更新，显示当日价格走势",中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,成交量显示,TC010,成交量显示,"1.查看任意股票
2.观察成交量数据
3.验证数据格式",成交量以万手或亿元为单位正确显示,中,等价类划分,功能测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,网络异常处理,TC011,网络断开时行情显示,"1.打开行情页面
2.断开网络连接
3.观察页面表现","显示网络错误提示，数据停止更新",高,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,无效代码处理,TC012,无效股票代码查询,"1.在搜索框输入""999999""
2.点击搜索
3.查看结果","提示""未找到相关股票""或类似错误信息",中,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,特殊字符处理,TC013,特殊字符输入测试,"1.在搜索框输入""@#$%""
2.点击搜索
3.查看系统响应","系统正常处理，不出现错误页面",中,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,长度限制测试,TC014,超长字符串输入,"1.输入超过100个字符的股票代码
2.提交查询
3.观察系统行为",系统限制输入长度或给出合理提示,低,边界值分析,异常测试,未执行,,
R001,系统应实时显示股票、基金、期货等金融产品行情数据,R001_行情数据_实时显示,用户访问行情页面时系统应显示最新的价格、涨跌幅、成交量等实时数据,超时处理,TC015,服务器响应超时,"1.模拟服务器响应延迟
2.访问行情页面
3.观察页面加载",显示加载提示或超时错误信息,中,边界值分析,性能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,新闻浏览,TC016,新闻正常浏览,"1.访问新浪财经首页
2.点击任意新闻标题
3.阅读新闻内容","新闻内容完整显示，包含标题、正文、时间、来源",高,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,新闻分类,TC017,新闻分类浏览,"1.点击""股票""分类
2.查看股票相关新闻
3.验证新闻相关性","显示股票相关新闻，分类准确",高,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,新闻搜索,TC018,新闻搜索功能,"1.在搜索框输入""茅台""
2.点击搜索
3.查看搜索结果","返回包含""茅台""关键词的相关新闻",中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,时间排序,TC019,新闻时间排序,"1.进入新闻列表页
2.查看新闻发布时间
3.验证排序规则",新闻按发布时间倒序排列,中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,分页功能,TC020,新闻分页功能,"1.浏览新闻列表
2.点击""下一页""
3.查看更多新闻","分页功能正常，可以查看更多历史新闻",中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,分享功能,TC021,新闻分享功能,"1.打开任意新闻
2.点击分享按钮
3.选择分享平台","可以分享到微博、微信等社交平台",低,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,相关推荐,TC022,相关新闻推荐,"1.阅读某条新闻
2.查看页面底部
3.验证相关新闻",显示相关主题的新闻推荐,低,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,图片显示,TC023,新闻图片显示,"1.打开包含图片的新闻
2.查看图片加载情况
3.点击图片放大","图片正常加载显示，支持放大查看",中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,评论查看,TC024,新闻评论查看,"1.打开新闻详情页
2.滚动到评论区
3.查看用户评论","显示用户评论，包含用户名、评论内容、时间",低,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,热门排行,TC025,热门新闻排行,"1.查看首页热门新闻
2.验证排行依据
3.点击查看详情",热门新闻按阅读量或热度排序,中,等价类划分,功能测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,空搜索处理,TC026,搜索空关键词,"1.在搜索框不输入任何内容
2.点击搜索
3.查看系统响应","提示""请输入搜索关键词""或类似提示",中,边界值分析,异常测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,特殊字符搜索,TC027,搜索特殊字符,"1.输入""<script>alert(1)</script>""
2.点击搜索
3.观察页面反应","系统正常处理，不执行脚本代码",高,边界值分析,安全测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,无效新闻访问,TC028,访问不存在的新闻,"1.修改URL中的新闻ID为无效值
2.访问该URL
3.查看页面显示","显示404错误页面或""新闻不存在""提示",中,边界值分析,异常测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,图片加载失败,TC029,新闻图片加载失败,"1.访问包含图片的新闻
2.模拟图片加载失败
3.查看页面表现",显示图片加载失败的占位符或提示,低,边界值分析,异常测试,未执行,,
R006,系统应及时发布最新财经新闻,R006_新闻_内容发布,编辑可以发布财经新闻新闻应包含标题、正文、发布时间、来源等信息,评论异常,TC030,评论功能异常,"1.尝试发表空评论
2.提交评论
3.观察系统响应","提示""评论内容不能为空""或类似错误信息",低,边界值分析,异常测试,未执行,,
新浪财经-004,系统提供技术分析指标计算,高,数据分析,软件需求说明书,系统计算并显示MACD、KDJ、RSI等技术指标帮助用户进行技术分析,R016,系统提供技术分析指标计算,R016_分析_技术指标,系统计算并显示MACD、KDJ、RSI等技术指标,MACD指标显示,TC031,MACD技术指标显示,"1.进入股票详情页;2.点击技术指标选项;3.选择MACD指标;4.查看指标数据","显示MACD指标线图和数值，包含DIF、DEA、MACD柱状图",等价类划分,功能测试,未执行,,
新浪财经-004,系统提供技术分析指标计算,高,数据分析,软件需求说明书,系统计算并显示MACD、KDJ、RSI等技术指标帮助用户进行技术分析,R016,系统提供技术分析指标计算,R016_分析_技术指标,系统计算并显示MACD、KDJ、RSI等技术指标,KDJ指标显示,TC032,KDJ技术指标显示,"1.进入股票详情页;2.点击技术指标选项;3.选择KDJ指标;4.查看指标数据","显示KDJ指标线图，包含K值、D值、J值三条线",等价类划分,功能测试,未执行,,
新浪财经-004,系统提供技术分析指标计算,高,数据分析,软件需求说明书,系统计算并显示MACD、KDJ、RSI等技术指标帮助用户进行技术分析,R016,系统提供技术分析指标计算,R016_分析_技术指标,系统计算并显示MACD、KDJ、RSI等技术指标,RSI指标显示,TC033,RSI技术指标显示,"1.进入股票详情页;2.点击技术指标选项;3.选择RSI指标;4.查看指标数据","显示RSI相对强弱指标，数值范围在0-100之间",等价类划分,功能测试,未执行,,
新浪财经-005,用户可以添加和管理自选股,中,自选股管理,软件需求说明书,登录用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,R004,用户可以添加和管理自选股,R004_行情数据_自选股,用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,添加自选股,TC034,添加股票到自选股,"1.搜索股票代码或名称;2.进入股票详情页;3.点击添加自选股按钮;4.确认添加","股票成功添加到自选股列表，按钮状态变为已添加",等价类划分,功能测试,未执行,,
新浪财经-005,用户可以添加和管理自选股,中,自选股管理,软件需求说明书,登录用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,R004,用户可以添加和管理自选股,R004_行情数据_自选股,用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,查看自选股,TC035,查看自选股列表,"1.点击自选股菜单;2.进入自选股页面;3.查看股票列表;4.验证股票信息","显示用户添加的自选股列表，包含股票名称、代码、当前价格、涨跌幅",等价类划分,功能测试,未执行,,
新浪财经-005,用户可以添加和管理自选股,中,自选股管理,软件需求说明书,登录用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,R004,用户可以添加和管理自选股,R004_行情数据_自选股,用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,删除自选股,TC036,删除自选股票,"1.进入自选股页面;2.选择要删除的股票;3.点击删除按钮;4.确认删除操作","股票从自选股列表中移除，列表更新显示",等价类划分,功能测试,未执行,,
新浪财经-005,用户可以添加和管理自选股,中,自选股管理,软件需求说明书,登录用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,R004,用户可以添加和管理自选股,R004_行情数据_自选股,用户可以添加股票到自选股列表支持增删改查操作最多支持100只股票,自选股排序,TC037,自选股列表排序,"1.进入自选股页面;2.点击排序选项;3.选择按涨跌幅排序;4.查看排序结果","自选股按照涨跌幅进行排序显示，涨幅大的在前",等价类划分,功能测试,未执行,,
新浪财经-006,系统提供价格预警功能,中,预警功能,软件需求说明书,用户可设置价格预警当股价达到设定值时发送通知,R019,系统提供价格预警功能,R019_分析_预警功能,用户可设置价格预警当股价达到设定值时发送通知,设置价格预警,TC038,设置股票价格预警,"1.进入股票详情页;2.点击设置预警;3.输入预警价格;4.选择预警方式;5.确认设置","预警设置成功，显示预警规则和状态",等价类划分,功能测试,未执行,,
新浪财经-006,系统提供价格预警功能,中,预警功能,软件需求说明书,用户可设置价格预警当股价达到设定值时发送通知,R019,系统提供价格预警功能,R019_分析_预警功能,用户可设置价格预警当股价达到设定值时发送通知,查看预警列表,TC039,查看价格预警列表,"1.进入预警管理页面;2.查看已设置的预警;3.验证预警信息;4.检查预警状态","显示用户设置的所有价格预警，包含股票名称、预警价格、当前价格、状态",等价类划分,功能测试,未执行,,
新浪财经-006,系统提供价格预警功能,中,预警功能,软件需求说明书,用户可设置价格预警当股价达到设定值时发送通知,R019,系统提供价格预警功能,R019_分析_预警功能,用户可设置价格预警当股价达到设定值时发送通知,删除预警,TC040,删除价格预警,"1.进入预警管理页面;2.选择要删除的预警;3.点击删除按钮;4.确认删除","预警成功删除，从预警列表中移除",等价类划分,功能测试,未执行,,
新浪财经-007,系统按涨跌幅排序显示股票排行榜,中,排行榜功能,软件需求说明书,系统按涨跌幅、成交量等指标对股票进行排序提供涨幅榜、跌幅榜等排行榜,R005,系统提供涨跌幅排行榜功能,R005_行情数据_排行榜,系统按涨跌幅、成交量等指标对股票进行排序提供涨幅榜、跌幅榜等排行榜,涨幅榜显示,TC041,查看涨幅排行榜,"1.进入股票排行榜页面;2.点击涨幅榜选项;3.查看排行榜数据;4.验证排序正确性","显示当日涨幅最大的股票排行，按涨幅从高到低排序",等价类划分,功能测试,未执行,,
新浪财经-007,系统按涨跌幅排序显示股票排行榜,中,排行榜功能,软件需求说明书,系统按涨跌幅、成交量等指标对股票进行排序提供涨幅榜、跌幅榜等排行榜,R005,系统提供涨跌幅排行榜功能,R005_行情数据_排行榜,系统按涨跌幅、成交量等指标对股票进行排序提供涨幅榜、跌幅榜等排行榜,跌幅榜显示,TC042,查看跌幅排行榜,"1.进入股票排行榜页面;2.点击跌幅榜选项;3.查看排行榜数据;4.验证排序正确性","显示当日跌幅最大的股票排行，按跌幅从高到低排序",等价类划分,功能测试,未执行,,
新浪财经-007,系统按涨跌幅排序显示股票排行榜,中,排行榜功能,软件需求说明书,系统按涨跌幅、成交量等指标对股票进行排序提供涨幅榜、跌幅榜等排行榜,R005,系统提供涨跌幅排行榜功能,R005_行情数据_排行榜,系统按涨跌幅、成交量等指标对股票进行排序提供涨幅榜、跌幅榜等排行榜,成交量榜显示,TC043,查看成交量排行榜,"1.进入股票排行榜页面;2.点击成交量榜选项;3.查看排行榜数据;4.验证数据准确性","显示当日成交量最大的股票排行，按成交量从高到低排序",等价类划分,功能测试,未执行,,
新浪财经-008,系统提供图表展示数据分析结果,中,图表展示,软件需求说明书,支持K线图、分时图、成交量图等多种图表展示,R017,系统以图表形式展示数据分析结果,R017_分析_图表展示,支持K线图、分时图、成交量图等多种图表展示,分时图切换,TC044,分时图时间周期切换,"1.进入股票详情页;2.点击分时图选项;3.切换不同时间周期;4.验证图表更新","分时图能够切换1分钟、5分钟、15分钟、30分钟、60分钟等时间周期",等价类划分,功能测试,未执行,,
新浪财经-008,系统提供图表展示数据分析结果,中,图表展示,软件需求说明书,支持K线图、分时图、成交量图等多种图表展示,R017,系统以图表形式展示数据分析结果,R017_分析_图表展示,支持K线图、分时图、成交量图等多种图表展示,K线图周期切换,TC045,K线图周期切换,"1.进入股票详情页;2.点击K线图选项;3.切换日K、周K、月K;4.验证图表数据","K线图能够正确切换日线、周线、月线，数据对应正确",等价类划分,功能测试,未执行,,
新浪财经-009,系统支持无效输入的异常处理,高,异常处理,软件需求说明书,系统对无效股票代码、特殊字符等异常输入进行正确处理,R012,系统对无效输入进行异常处理,R012_异常处理_输入验证,系统对无效股票代码、特殊字符等异常输入进行正确处理,无效预警价格,TC046,无效预警价格输入,"1.进入价格预警设置;2.输入负数价格;3.尝试保存设置;4.查看系统响应","系统提示价格必须为正数，不允许设置无效预警",边界值分析,异常测试,未执行,,
新浪财经-009,系统支持无效输入的异常处理,高,异常处理,软件需求说明书,系统对无效股票代码、特殊字符等异常输入进行正确处理,R012,系统对无效输入进行异常处理,R012_异常处理_输入验证,系统对无效股票代码、特殊字符等异常输入进行正确处理,超出自选股限制,TC047,超出自选股数量限制,"1.添加100只自选股;2.尝试添加第101只股票;3.查看系统响应;4.验证限制机制","系统提示已达到自选股数量上限，不允许继续添加",边界值分析,异常测试,未执行,,
新浪财经-009,系统支持无效输入的异常处理,高,异常处理,软件需求说明书,系统对无效股票代码、特殊字符等异常输入进行正确处理,R012,系统对无效输入进行异常处理,R012_异常处理_输入验证,系统对无效股票代码、特殊字符等异常输入进行正确处理,重复添加自选股,TC048,重复添加相同自选股,"1.添加某只股票到自选股;2.再次尝试添加同一只股票;3.查看系统响应;4.验证去重机制","系统提示该股票已在自选股中，不重复添加",边界值分析,异常测试,未执行,,
新浪财经-010,系统提供数据导出功能,低,数据导出,软件需求说明书,VIP用户可以导出Excel格式的行情数据和分析报告,R018,用户可以导出分析数据,R018_分析_数据导出,VIP用户可以导出Excel格式的行情数据和分析报告,导出行情数据,TC049,导出股票行情数据,"1.进入股票详情页;2.点击数据导出功能;3.选择导出格式;4.确认导出操作","成功导出Excel格式的股票行情数据，包含价格、成交量等信息",等价类划分,功能测试,未执行,,
新浪财经-010,系统提供数据导出功能,低,数据导出,软件需求说明书,VIP用户可以导出Excel格式的行情数据和分析报告,R018,用户可以导出分析数据,R018_分析_数据导出,VIP用户可以导出Excel格式的行情数据和分析报告,导出技术分析,TC050,导出技术分析报告,"1.查看技术指标图表;2.点击导出分析报告;3.选择报告格式;4.下载文件","成功导出包含技术指标分析的报告文件，格式为Excel或PDF",等价类划分,功能测试,未执行,,
