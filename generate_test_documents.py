#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成测试需求文档和测试用例文档的Excel文件
基于现有的测试用例汇总表和模板生成标准的测试文档
"""

import pandas as pd
from datetime import datetime
import os

def create_test_requirements_document():
    """创建测试需求文档Excel"""
    
    # 测试需求数据
    requirements_data = [
        {
            '需求编号': 'R001',
            '需求名称': '行情数据实时显示',
            '需求描述': '系统应实时显示股票、基金、期货等金融产品行情数据，包括价格、涨跌幅、成交量等信息，数据延迟不超过15分钟',
            '优先级': '高',
            '需求来源': '软件需求说明书',
            '功能模块': '行情数据',
            '验收标准': '1.页面能正常显示各类金融产品行情\n2.数据更新及时，延迟不超过15分钟\n3.数据格式正确，包含必要字段\n4.支持实时刷新',
            '依赖关系': '无',
            '风险评估': '中',
            '备注': '核心功能，影响用户体验'
        },
        {
            '需求编号': 'R006',
            '需求名称': '新闻内容发布',
            '需求描述': '系统应及时发布最新财经新闻，新闻应包含标题、正文、发布时间、来源等信息，支持分类浏览和搜索',
            '优先级': '高',
            '需求来源': '软件需求说明书',
            '功能模块': '新闻资讯',
            '验收标准': '1.新闻内容完整显示\n2.支持按分类浏览\n3.搜索功能正常\n4.新闻时间排序正确',
            '依赖关系': '无',
            '风险评估': '低',
            '备注': '内容展示功能'
        },
        {
            '需求编号': 'R008',
            '需求名称': '新闻搜索功能',
            '需求描述': '用户可以通过关键词搜索相关财经新闻，支持模糊搜索和精确搜索',
            '优先级': '中',
            '需求来源': '软件需求说明书',
            '功能模块': '新闻资讯',
            '验收标准': '1.搜索结果准确\n2.支持关键词高亮\n3.搜索速度快\n4.支持搜索历史',
            '依赖关系': 'R006',
            '风险评估': '低',
            '备注': '增强用户体验'
        },
        {
            '需求编号': 'R016',
            '需求名称': '技术分析指标',
            '需求描述': '系统计算并显示MACD、KDJ、RSI等技术指标，帮助用户进行技术分析',
            '优先级': '高',
            '需求来源': '软件需求说明书',
            '功能模块': '数据分析',
            '验收标准': '1.技术指标计算准确\n2.图表显示清晰\n3.支持多种指标切换\n4.数据实时更新',
            '依赖关系': 'R001',
            '风险评估': '中',
            '备注': '专业分析功能'
        },
        {
            '需求编号': 'R004',
            '需求名称': '自选股管理',
            '需求描述': '用户可以添加股票到自选股列表，支持增删改查操作，最多支持100只股票',
            '优先级': '中',
            '需求来源': '软件需求说明书',
            '功能模块': '自选股管理',
            '验收标准': '1.添加删除功能正常\n2.列表显示完整\n3.支持排序功能\n4.数量限制有效',
            '依赖关系': 'R001',
            '风险评估': '低',
            '备注': '个性化功能'
        },
        {
            '需求编号': 'R019',
            '需求名称': '价格预警功能',
            '需求描述': '用户可设置价格预警，当股价达到设定值时发送通知',
            '优先级': '中',
            '需求来源': '软件需求说明书',
            '功能模块': '预警功能',
            '验收标准': '1.预警设置成功\n2.触发通知及时\n3.预警管理完善\n4.通知方式多样',
            '依赖关系': 'R001',
            '风险评估': '中',
            '备注': '增值服务功能'
        },
        {
            '需求编号': 'R005',
            '需求名称': '排行榜功能',
            '需求描述': '系统按涨跌幅、成交量等指标对股票进行排序，提供涨幅榜、跌幅榜等排行榜',
            '优先级': '中',
            '需求来源': '软件需求说明书',
            '功能模块': '排行榜功能',
            '验收标准': '1.排序算法正确\n2.数据更新及时\n3.分类清晰\n4.显示完整',
            '依赖关系': 'R001',
            '风险评估': '低',
            '备注': '数据展示功能'
        },
        {
            '需求编号': 'R017',
            '需求名称': '图表展示',
            '需求描述': '支持K线图、分时图、成交量图等多种图表展示',
            '优先级': '中',
            '需求来源': '软件需求说明书',
            '功能模块': '图表展示',
            '验收标准': '1.图表显示正确\n2.交互功能完善\n3.切换流畅\n4.数据准确',
            '依赖关系': 'R001',
            '风险评估': '中',
            '备注': '可视化功能'
        },
        {
            '需求编号': 'R012',
            '需求名称': '异常处理',
            '需求描述': '系统对无效股票代码、特殊字符等异常输入进行正确处理',
            '优先级': '高',
            '需求来源': '软件需求说明书',
            '功能模块': '异常处理',
            '验收标准': '1.错误提示清晰\n2.系统稳定性好\n3.用户体验友好\n4.安全性保障',
            '依赖关系': '所有功能模块',
            '风险评估': '高',
            '备注': '系统稳定性保障'
        },
        {
            '需求编号': 'R018',
            '需求名称': '数据导出',
            '需求描述': 'VIP用户可以导出Excel格式的行情数据和分析报告',
            '优先级': '低',
            '需求来源': '软件需求说明书',
            '功能模块': '数据导出',
            '验收标准': '1.导出格式正确\n2.数据完整性\n3.权限控制有效\n4.文件可正常打开',
            '依赖关系': 'R001, R016',
            '风险评估': '低',
            '备注': 'VIP增值功能'
        }
    ]
    
    # 创建DataFrame
    df_requirements = pd.DataFrame(requirements_data)
    
    # 创建Excel文件
    filename = f"测试需求文档_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入需求概述工作表
        df_requirements.to_excel(writer, sheet_name='测试需求概述', index=False)
        
        # 创建需求统计工作表
        stats_data = {
            '统计项目': ['总需求数', '高优先级需求', '中优先级需求', '低优先级需求', '功能模块数'],
            '数量': [
                len(df_requirements),
                len(df_requirements[df_requirements['优先级'] == '高']),
                len(df_requirements[df_requirements['优先级'] == '中']),
                len(df_requirements[df_requirements['优先级'] == '低']),
                len(df_requirements['功能模块'].unique())
            ]
        }
        df_stats = pd.DataFrame(stats_data)
        df_stats.to_excel(writer, sheet_name='需求统计', index=False)
        
        # 创建需求跟踪矩阵工作表
        traceability_data = []
        for _, req in df_requirements.iterrows():
            traceability_data.append({
                '需求编号': req['需求编号'],
                '需求名称': req['需求名称'],
                '测试用例覆盖': '是',
                '测试执行状态': '待执行',
                '缺陷关联': '无',
                '验收状态': '待验收'
            })
        
        df_trace = pd.DataFrame(traceability_data)
        df_trace.to_excel(writer, sheet_name='需求跟踪矩阵', index=False)
    
    print(f"✓ 测试需求文档已生成: {filename}")
    return filename

def create_test_cases_document():
    """创建测试用例文档Excel"""
    
    # 从CSV文件读取测试用例数据
    try:
        df_csv = pd.read_csv('测试用例汇总表.csv', encoding='utf-8')
    except:
        df_csv = pd.read_csv('测试用例汇总表.csv', encoding='gbk')
    
    # 处理测试用例数据
    test_cases_data = []
    
    for _, row in df_csv.iterrows():
        test_cases_data.append({
            '测试用例编号': row['测试用例编号'],
            '测试用例标题': row['测试用例标题'],
            '所属模块': row['所属功能模块'],
            '优先级': row['优先级'],
            '测试类型': row['测试类型'],
            '测试方法': row['测试方法'],
            '前置条件': '1. 浏览器正常运行\n2. 网络连接正常\n3. 访问新浪财经网站',
            '测试步骤': row['测试步骤'],
            '预期结果': row['预期结果'],
            '实际结果': '',
            '测试状态': '未执行',
            '执行人': '',
            '执行时间': '',
            '缺陷编号': '',
            '备注': ''
        })
    
    df_test_cases = pd.DataFrame(test_cases_data)
    
    # 创建Excel文件
    filename = f"测试用例文档_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入测试用例详情工作表
        df_test_cases.to_excel(writer, sheet_name='测试用例详情', index=False)
        
        # 创建测试用例统计工作表
        stats_data = {
            '统计项目': [
                '总用例数',
                '高优先级用例',
                '中优先级用例', 
                '低优先级用例',
                '功能测试用例',
                '异常测试用例',
                '性能测试用例',
                '安全测试用例'
            ],
            '数量': [
                len(df_test_cases),
                len(df_test_cases[df_test_cases['优先级'] == '高']),
                len(df_test_cases[df_test_cases['优先级'] == '中']),
                len(df_test_cases[df_test_cases['优先级'] == '低']),
                len(df_test_cases[df_test_cases['测试类型'] == '功能测试']),
                len(df_test_cases[df_test_cases['测试类型'] == '异常测试']),
                len(df_test_cases[df_test_cases['测试类型'] == '性能测试']),
                len(df_test_cases[df_test_cases['测试类型'] == '安全测试'])
            ]
        }
        df_stats = pd.DataFrame(stats_data)
        df_stats.to_excel(writer, sheet_name='用例统计', index=False)
        
        # 创建测试执行记录工作表
        execution_data = []
        for _, case in df_test_cases.iterrows():
            execution_data.append({
                '测试用例编号': case['测试用例编号'],
                '测试用例标题': case['测试用例标题'],
                '执行轮次': '第1轮',
                '执行人': '',
                '执行时间': '',
                '执行结果': '未执行',
                '缺陷数量': 0,
                '备注': ''
            })
        
        df_execution = pd.DataFrame(execution_data)
        df_execution.to_excel(writer, sheet_name='执行记录', index=False)
    
    print(f"✓ 测试用例文档已生成: {filename}")
    return filename

def main():
    """主函数"""
    print("=" * 60)
    print("新浪财经测试文档生成工具")
    print("=" * 60)
    
    try:
        # 生成测试需求文档
        req_file = create_test_requirements_document()
        
        # 生成测试用例文档  
        case_file = create_test_cases_document()
        
        print("\n" + "=" * 60)
        print("文档生成完成!")
        print(f"测试需求文档: {req_file}")
        print(f"测试用例文档: {case_file}")
        print("=" * 60)
        
    except Exception as e:
        print(f"生成文档时发生错误: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
