#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪财经技术分析指标功能自动化测试
测试目标：技术分析指标计算和显示功能
对应需求：R016_分析_技术指标
"""

import unittest
import time
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys


class TechnicalAnalysisAutomationTest(unittest.TestCase):
    """技术分析指标功能自动化测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化设置"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        cls.driver = webdriver.Chrome(options=chrome_options)
        cls.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        cls.driver.implicitly_wait(10)
        cls.wait = WebDriverWait(cls.driver, 15)
        cls.base_url = "https://finance.sina.com.cn/"

        # 测试股票代码
        cls.test_stocks = {
            'sh000001': '上证指数',
            'sz399001': '深证成指',
            'sh600519': '贵州茅台',
            'sz000858': '五粮液',
            'sh600036': '招商银行'
        }
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        if cls.driver:
            cls.driver.quit()
    
    def setUp(self):
        """每个测试用例前的设置"""
        self.driver.get(self.base_url)
        time.sleep(2)

    def find_stock_entry(self, stock_name="股票"):
        """查找股票入口"""
        stock_selectors = [
            f"//a[contains(text(), '{stock_name}')]",
            f"//a[contains(@href, 'stock')]",
            "//a[contains(text(), '行情')]",
            "//a[contains(text(), '沪深')]"
        ]

        for selector in stock_selectors:
            try:
                stock_element = self.driver.find_element(By.XPATH, selector)
                if stock_element.is_displayed():
                    return stock_element
            except NoSuchElementException:
                continue
        return None

    def navigate_to_stock_detail(self, stock_code):
        """导航到股票详情页"""
        try:
            # 尝试直接访问股票详情页
            stock_url = f"https://finance.sina.com.cn/realstock/company/{stock_code}/nc.shtml"
            self.driver.get(stock_url)
            time.sleep(3)
            return True
        except Exception:
            return False
    
    def test_01_macd_indicator_display(self):
        """
        测试用例1：MACD技术指标显示测试
        测试步骤：
        1. 进入股票详情页
        2. 点击技术指标选项
        3. 选择MACD指标
        4. 查看指标数据
        """
        print("\n=== 执行测试用例1：MACD技术指标显示测试 ===")

        try:
            # 导航到股票详情页
            stock_code = 'sh600519'  # 贵州茅台
            success = self.navigate_to_stock_detail(stock_code)

            if not success:
                # 尝试通过首页导航
                stock_entry = self.find_stock_entry()
                if stock_entry:
                    print(f"✓ 找到股票入口: {stock_entry.text}")
                    stock_entry.click()
                    time.sleep(3)
                else:
                    print("未找到股票入口，尝试搜索功能")

            # 查找技术指标相关元素
            indicator_selectors = [
                "//a[contains(text(), '技术指标')]",
                "//a[contains(text(), 'MACD')]",
                "//div[contains(@class, 'indicator')]",
                "//div[contains(@class, 'technical')]",
                "//canvas",  # 图表画布
                "//svg"      # SVG图表
            ]

            indicator_found = False
            for selector in indicator_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✓ 找到技术指标相关元素: {len(elements)}个 ({selector})")
                        indicator_found = True
                        break
                except Exception:
                    continue

            # 检查页面是否包含MACD相关内容
            page_content = self.driver.page_source.lower()
            macd_keywords = ['macd', 'dif', 'dea', '技术指标', '指标', '均线']
            keyword_found = any(keyword in page_content for keyword in macd_keywords)

            if keyword_found:
                print("✓ 页面包含MACD相关关键词")

            # 验证MACD指标显示
            macd_display_success = indicator_found or keyword_found
            self.assertTrue(macd_display_success, "应该能够显示MACD技术指标")

            print("✓ MACD技术指标显示测试通过")

        except Exception as e:
            print(f"✗ MACD技术指标显示测试失败: {str(e)}")
            self.fail(f"MACD技术指标显示测试失败: {str(e)}")
    
    def test_02_kdj_indicator_display(self):
        """
        测试用例2：KDJ技术指标显示测试
        测试步骤：
        1. 进入股票详情页
        2. 点击技术指标选项
        3. 选择KDJ指标
        4. 查看指标数据
        """
        print("\n=== 执行测试用例2：KDJ技术指标显示测试 ===")

        try:
            # 导航到股票详情页
            stock_code = 'sz000858'  # 五粮液
            success = self.navigate_to_stock_detail(stock_code)

            if not success:
                # 尝试通过首页导航
                stock_entry = self.find_stock_entry()
                if stock_entry:
                    print(f"✓ 找到股票入口: {stock_entry.text}")
                    stock_entry.click()
                    time.sleep(3)

            # 查找KDJ指标相关元素
            kdj_selectors = [
                "//a[contains(text(), 'KDJ')]",
                "//div[contains(text(), 'KDJ')]",
                "//span[contains(text(), 'KDJ')]",
                "//div[contains(@class, 'kdj')]",
                "//canvas",  # 图表画布
                "//svg"      # SVG图表
            ]

            kdj_found = False
            for selector in kdj_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✓ 找到KDJ指标相关元素: {len(elements)}个")
                        kdj_found = True
                        break
                except Exception:
                    continue

            # 检查页面是否包含KDJ相关内容
            page_content = self.driver.page_source.lower()
            kdj_keywords = ['kdj', 'k值', 'd值', 'j值', '随机指标', '技术指标']
            keyword_found = any(keyword in page_content for keyword in kdj_keywords)

            if keyword_found:
                print("✓ 页面包含KDJ相关关键词")

            # 查找图表元素
            chart_selectors = [
                "//canvas",
                "//svg",
                "//div[contains(@class, 'chart')]",
                "//div[contains(@class, 'graph')]"
            ]

            chart_found = False
            for selector in chart_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✓ 找到图表元素: {len(elements)}个")
                        chart_found = True
                        break
                except Exception:
                    continue

            # 验证KDJ指标显示
            kdj_display_success = kdj_found or keyword_found or chart_found
            self.assertTrue(kdj_display_success, "应该能够显示KDJ技术指标")

            print("✓ KDJ技术指标显示测试通过")

        except Exception as e:
            print(f"✗ KDJ技术指标显示测试失败: {str(e)}")
            self.fail(f"KDJ技术指标显示测试失败: {str(e)}")
    
    def test_03_rsi_indicator_display(self):
        """
        测试用例3：RSI技术指标显示测试
        测试步骤：
        1. 进入股票详情页
        2. 点击技术指标选项
        3. 选择RSI指标
        4. 查看指标数据
        """
        print("\n=== 执行测试用例3：RSI技术指标显示测试 ===")

        try:
            # 导航到股票详情页
            stock_code = 'sh600036'  # 招商银行
            success = self.navigate_to_stock_detail(stock_code)

            if not success:
                # 尝试通过首页导航
                stock_entry = self.find_stock_entry()
                if stock_entry:
                    print(f"✓ 找到股票入口: {stock_entry.text}")
                    stock_entry.click()
                    time.sleep(3)

            # 查找RSI指标相关元素
            rsi_selectors = [
                "//a[contains(text(), 'RSI')]",
                "//div[contains(text(), 'RSI')]",
                "//span[contains(text(), 'RSI')]",
                "//div[contains(@class, 'rsi')]",
                "//canvas",  # 图表画布
                "//svg"      # SVG图表
            ]

            rsi_found = False
            for selector in rsi_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✓ 找到RSI指标相关元素: {len(elements)}个")
                        rsi_found = True
                        break
                except Exception:
                    continue

            # 检查页面是否包含RSI相关内容
            page_content = self.driver.page_source.lower()
            rsi_keywords = ['rsi', '相对强弱', '强弱指标', '技术指标', '超买', '超卖']
            keyword_found = any(keyword in page_content for keyword in rsi_keywords)

            if keyword_found:
                print("✓ 页面包含RSI相关关键词")

            # 查找数值范围验证（RSI应该在0-100之间）
            try:
                # 使用正则表达式查找可能的RSI数值
                rsi_pattern = r'rsi[:\s]*(\d{1,3}\.?\d*)'
                matches = re.findall(rsi_pattern, page_content, re.IGNORECASE)

                valid_rsi_found = False
                for match in matches:
                    try:
                        value = float(match)
                        if 0 <= value <= 100:
                            print(f"✓ 找到有效的RSI数值: {value}")
                            valid_rsi_found = True
                            break
                    except ValueError:
                        continue

                if valid_rsi_found:
                    rsi_found = True

            except Exception:
                pass

            # 查找图表元素
            chart_selectors = [
                "//canvas",
                "//svg",
                "//div[contains(@class, 'chart')]",
                "//div[contains(@class, 'graph')]"
            ]

            chart_found = False
            for selector in chart_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"✓ 找到图表元素: {len(elements)}个")
                        chart_found = True
                        break
                except Exception:
                    continue

            # 验证RSI指标显示
            rsi_display_success = rsi_found or keyword_found or chart_found
            self.assertTrue(rsi_display_success, "应该能够显示RSI技术指标")

            print("✓ RSI技术指标显示测试通过")

        except Exception as e:
            print(f"✗ RSI技术指标显示测试失败: {str(e)}")
            self.fail(f"RSI技术指标显示测试失败: {str(e)}")


def run_technical_analysis_tests():
    """运行技术分析指标相关测试用例"""
    print("=" * 60)
    print("新浪财经技术分析指标功能自动化测试开始")
    print("=" * 60)

    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TechnicalAnalysisAutomationTest)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    print("\n" + "=" * 60)
    print("技术分析指标测试结果汇总:")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print("=" * 60)

    return result


if __name__ == "__main__":
    # 运行测试
    test_result = run_technical_analysis_tests()

    # 根据测试结果设置退出码
    if test_result.failures or test_result.errors:
        exit(1)
    else:
        exit(0)
